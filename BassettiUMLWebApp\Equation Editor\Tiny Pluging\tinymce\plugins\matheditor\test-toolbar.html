<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Math Editor Toolbar Test</title>
    <link rel="stylesheet" href="matheditor.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/mathlive@0.95.0/dist/mathlive.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .description {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #0078d4;
        }
        .test-button {
            background: #0078d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .test-button:hover {
            background: #106ebe;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Math Editor - Microsoft Word Style Toolbar</h1>
        
        <div class="description">
            <h3>New Features:</h3>
            <ul>
                <li><strong>Flat Button Design:</strong> Buttons now have a clean, flat appearance similar to Microsoft Word</li>
                <li><strong>Logical Grouping:</strong> Related symbols are organized into groups with vertical dividers</li>
                <li><strong>Group Labels:</strong> Each group has a descriptive label positioned below the buttons</li>
                <li><strong>Hover Effects:</strong> Microsoft Word-style hover effects with light blue highlighting</li>
                <li><strong>Responsive Layout:</strong> Groups adapt to available space while maintaining organization</li>
            </ul>
        </div>

        <button class="test-button" onclick="openMathEditor()">Open Math Editor</button>

        <!-- Math Editor Dialog (initially hidden) -->
        <div class="matheditor-dialog" style="display: none;" id="mathEditorDialog">
            <div class="matheditor-content">
                <div class="matheditor-header">
                    <h2>Math Editor</h2>
                    <button class="matheditor-close-btn" onclick="closeMathEditor()">&times;</button>
                </div>

                <div class="matheditor-container">
                    <!-- Toolbar Tabs -->
                    <div class="matheditor-toolbar-tabs">
                        <button class="matheditor-tab-btn active" data-tab="basic">Basic</button>
                        <button class="matheditor-tab-btn" data-tab="operators">Operators</button>
                        <button class="matheditor-tab-btn" data-tab="relations">Relations</button>
                        <button class="matheditor-tab-btn" data-tab="greek">Greek Letters</button>
                        <button class="matheditor-tab-btn" data-tab="arrows">Arrows</button>
                        <button class="matheditor-tab-btn" data-tab="functions">Functions</button>
                        <button class="matheditor-tab-btn" data-tab="matrices">Matrices</button>
                    </div>

                    <!-- Toolbar Content -->
                    <div class="matheditor-toolbar">
                        <!-- Basic Tab Content (visible by default) -->
                        <div class="matheditor-tab-content active" id="basic-tab">
                            <!-- Fractions & Roots Group -->
                            <div class="matheditor-button-group">
                                <button class="matheditor-toolbar-btn large" title="Fraction">
                                    <i class="fal fa-divide"></i>
                                    <span class="btn-text">a/b</span>
                                </button>
                                <button class="matheditor-toolbar-btn" title="Square Root">
                                    <i class="fal fa-square-root-alt"></i>
                                </button>
                                <button class="matheditor-toolbar-btn" title="Cube Root">
                                    <i class="fal fa-cube-root"></i>
                                </button>
                                <button class="matheditor-toolbar-btn" title="nth Root">
                                    <i class="fal fa-function"></i>
                                </button>
                                <div class="matheditor-group-label">Fractions & Roots</div>
                            </div>

                            <!-- Powers & Indices Group -->
                            <div class="matheditor-button-group">
                                <button class="matheditor-toolbar-btn" title="Exponent">
                                    <i class="fal fa-superscript"></i>
                                </button>
                                <button class="matheditor-toolbar-btn" title="Subscript">
                                    <i class="fal fa-subscript"></i>
                                </button>
                                <div class="matheditor-group-label">Powers & Indices</div>
                            </div>

                            <!-- Common Symbols Group -->
                            <div class="matheditor-button-group">
                                <button class="matheditor-toolbar-btn" title="Pi">π</button>
                                <button class="matheditor-toolbar-btn" title="Infinity">∞</button>
                                <button class="matheditor-toolbar-btn" title="Degree">°</button>
                                <button class="matheditor-toolbar-btn" title="Plus Minus">±</button>
                                <div class="matheditor-group-label">Common Symbols</div>
                            </div>
                        </div>

                        <!-- Other tab contents would be populated dynamically -->
                        <div class="matheditor-tab-content" id="operators-tab"></div>
                        <div class="matheditor-tab-content" id="relations-tab"></div>
                        <div class="matheditor-tab-content" id="greek-tab"></div>
                        <div class="matheditor-tab-content" id="arrows-tab"></div>
                        <div class="matheditor-tab-content" id="functions-tab"></div>
                        <div class="matheditor-tab-content" id="matrices-tab"></div>
                    </div>

                    <div class="matheditor-input-container">
                        <label class="matheditor-label">Edit Equation:</label>
                        <math-field id="matheditor-input"></math-field>
                    </div>

                    <div class="matheditor-buttons-container">
                        <button class="matheditor-cancel-btn" onclick="closeMathEditor()">Cancel</button>
                        <button class="matheditor-insert-btn">Insert</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="matheditor.js"></script>
    <script>
        function openMathEditor() {
            document.getElementById('mathEditorDialog').style.display = 'flex';
            
            // Initialize MathLive if not already done
            const mathField = document.getElementById('matheditor-input');
            if (mathField && !mathField.mathVirtualKeyboardPolicy) {
                mathField.mathVirtualKeyboardPolicy = 'off';
                mathField.smartMode = true;
                mathField.smartFence = true;
                mathField.letterShapeStyle = 'tex';
            }
        }

        function closeMathEditor() {
            document.getElementById('mathEditorDialog').style.display = 'none';
        }

        // Tab switching functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.matheditor-tab-btn');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');
                    
                    // Remove active class from all tabs and content
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    document.querySelectorAll('.matheditor-tab-content').forEach(content => 
                        content.classList.remove('active')
                    );
                    
                    // Add active class to clicked tab and corresponding content
                    this.classList.add('active');
                    const targetContent = document.getElementById(targetTab + '-tab');
                    if (targetContent) {
                        targetContent.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>
